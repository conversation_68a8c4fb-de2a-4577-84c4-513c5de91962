#!/usr/bin/env node

/**
 * AceServe Application Startup Script
 *
 * This script starts the entire AceServe application stack including:
 * - Docker services (PostgreSQL, Redis, Elasticsearch)
 * - Backend API (Node.js/Fastify)
 * - Frontend (Next.js)
 * - Scraper service (Python/FastAPI)
 *
 * Enhanced Features:
 * - Comprehensive health checks for all services
 * - Service dependency validation
 * - Progress tracking and resume capability
 * - Integrated testing and validation
 * - Enhanced error handling with retry logic
 * - Structured logging and monitoring
 *
 * Usage: node start.js [options]
 * Options:
 *   --skip-docker    Skip Docker services (use existing)
 *   --skip-migrate   Skip database migrations
 *   --skip-seed      Skip database seeding
 *   --no-browser     Don't open browser automatically
 *   --skip-tests     Skip comprehensive testing after startup
 *   --quick-start    Skip health checks and testing (faster startup)
 *   --retry-failed   Retry failed services automatically
 *   --help           Show this help message
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const readline = require('readline');
const os = require('os');
const http = require('http');
const https = require('https');

// Load environment variables
require('dotenv').config();

// Terminal colors
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Configuration - Load from environment variables with defaults
const config = {
  services: {
    docker: { name: 'Docker Services', color: colors.blue },
    backend: {
      name: 'Backend API',
      port: parseInt(process.env.BACKEND_PORT) || 3000,
      color: colors.green,
      healthPath: '/health'
    },
    frontend: {
      name: 'Frontend',
      port: parseInt(process.env.FRONTEND_PORT) || 3001,
      color: colors.cyan,
      healthPath: '/'
    },
    scraper: {
      name: 'Scraper Service',
      port: parseInt(process.env.SCRAPER_PORT) || 8001,
      color: colors.yellow,
      healthPath: '/health'
    }
  },
  paths: {
    backend: path.join(__dirname, 'backend'),
    frontend: path.join(__dirname, 'frontend'),
    scraper: path.join(__dirname, 'scraper'),
    dockerCompose: path.join(__dirname, 'docker-compose.yml'),
    progressFile: path.join(__dirname, 'progress.md')
  },
  urls: {
    frontend: `http://localhost:${parseInt(process.env.FRONTEND_PORT) || 3001}`,
    backend: `http://localhost:${parseInt(process.env.BACKEND_PORT) || 3000}`,
    api: `http://localhost:${parseInt(process.env.BACKEND_PORT) || 3000}/documentation`,
    scraper: `http://localhost:${parseInt(process.env.SCRAPER_PORT) || 8001}`,
    adminer: 'http://localhost:8080',
    redisCommander: 'http://localhost:8081'
  },
  timeouts: {
    serviceStart: 60000,    // 60 seconds for service startup
    healthCheck: 10000,     // 10 seconds for health checks
    dockerStart: 120000,    // 2 minutes for Docker services
    maxRetries: 3           // Maximum retry attempts
  }
};

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  skipDocker: args.includes('--skip-docker'),
  skipMigrate: args.includes('--skip-migrate'),
  skipSeed: args.includes('--skip-seed'),
  noBrowser: args.includes('--no-browser'),
  skipTests: args.includes('--skip-tests'),
  quickStart: args.includes('--quick-start'),
  retryFailed: args.includes('--retry-failed'),
  help: args.includes('--help')
};

// Active processes to manage
const processes = new Map();
let isShuttingDown = false;
let startupProgress = {
  prerequisites: false,
  environment: false,
  docker: false,
  dependencies: false,
  database: false,
  backend: false,
  frontend: false,
  scraper: false,
  healthChecks: false,
  testing: false
};

// Utility functions
function log(message, color = colors.white) {
  const timestamp = new Date().toISOString();
  console.log(`${colors.dim}[${timestamp}]${colors.reset} ${color}${message}${colors.reset}`);
}

function logService(service, message) {
  const serviceConfig = config.services[service];
  const timestamp = new Date().toISOString();
  console.log(`${colors.dim}[${timestamp}]${colors.reset} ${serviceConfig.color}[${serviceConfig.name}]${colors.reset} ${message}`);
}

function logError(message) {
  const timestamp = new Date().toISOString();
  console.error(`${colors.dim}[${timestamp}]${colors.reset} ${colors.red}✗ ${message}${colors.reset}`);
}

function logSuccess(message) {
  const timestamp = new Date().toISOString();
  console.log(`${colors.dim}[${timestamp}]${colors.reset} ${colors.green}✓ ${message}${colors.reset}`);
}

function logWarning(message) {
  const timestamp = new Date().toISOString();
  console.log(`${colors.dim}[${timestamp}]${colors.reset} ${colors.yellow}⚠ ${message}${colors.reset}`);
}

// Progress tracking functions
function updateProgress(step, status = true, details = '') {
  startupProgress[step] = status;
  saveProgress(details);
}

function saveProgress(details = '') {
  const progressContent = `# AceServe Startup Progress

**Last Updated:** ${new Date().toISOString()}

## Startup Steps Status

${Object.entries(startupProgress).map(([step, status]) =>
  `- [${status ? 'x' : ' '}] ${step.charAt(0).toUpperCase() + step.slice(1)}`
).join('\n')}

## Details
${details}

## Services Configuration
- Backend Port: ${config.services.backend.port}
- Frontend Port: ${config.services.frontend.port}
- Scraper Port: ${config.services.scraper.port}

## Access URLs
- Frontend: ${config.urls.frontend}
- Backend API: ${config.urls.backend}
- API Documentation: ${config.urls.api}
- Scraper Service: ${config.urls.scraper}
- Database UI: ${config.urls.adminer}
- Redis UI: ${config.urls.redisCommander}
`;

  try {
    fs.writeFileSync(config.paths.progressFile, progressContent);
  } catch (error) {
    logWarning(`Could not save progress file: ${error.message}`);
  }
}

// HTTP Health Check Functions
function httpHealthCheck(url, timeout = config.timeouts.healthCheck) {
  return new Promise((resolve) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: 'GET',
      timeout: timeout
    };

    const req = http.request(options, (res) => {
      resolve({
        success: res.statusCode >= 200 && res.statusCode < 400,
        statusCode: res.statusCode,
        url: url
      });
    });

    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message,
        url: url
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Request timeout',
        url: url
      });
    });

    req.end();
  });
}

async function waitForHttpService(name, url, maxRetries = 30, retryDelay = 2000) {
  log(`Waiting for ${name} to respond at ${url}...`, colors.yellow);

  for (let i = 0; i < maxRetries; i++) {
    const result = await httpHealthCheck(url);

    if (result.success) {
      logSuccess(`${name} is responding (HTTP ${result.statusCode})`);
      return true;
    }

    if (i < maxRetries - 1) {
      process.stdout.write('.');
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  logError(`${name} failed to respond after ${maxRetries} attempts`);
  return false;
}

async function validateServiceHealth(serviceName) {
  const service = config.services[serviceName];
  if (!service) return false;

  const url = `http://localhost:${service.port}${service.healthPath}`;
  const result = await httpHealthCheck(url);

  if (result.success) {
    logSuccess(`${service.name} health check passed`);
    return true;
  } else {
    logError(`${service.name} health check failed: ${result.error || `HTTP ${result.statusCode}`}`);
    return false;
  }
}

// Enhanced service dependency validation
async function validateServiceDependencies() {
  log('Validating service dependencies...', colors.yellow);

  const checks = [];

  // Check database connectivity
  checks.push({
    name: 'PostgreSQL Connection',
    check: () => execCommand('docker exec aceserve-postgres pg_isready -U postgres')
  });

  // Check Redis connectivity
  checks.push({
    name: 'Redis Connection',
    check: () => execCommand('docker exec aceserve-redis redis-cli ping')
  });

  // Check external services if API keys are configured
  if (process.env.GEMINI_API_KEY && process.env.GEMINI_API_KEY !== 'your-gemini-api-key-here') {
    checks.push({
      name: 'Gemini API',
      check: async () => {
        const result = await httpHealthCheck('https://generativelanguage.googleapis.com/v1/models?key=' + process.env.GEMINI_API_KEY);
        if (!result.success) throw new Error(result.error || 'API not accessible');
        return result;
      }
    });
  }

  let allPassed = true;
  for (const check of checks) {
    try {
      await check.check();
      logSuccess(`${check.name} validation passed`);
    } catch (error) {
      logError(`${check.name} validation failed: ${error.message}`);
      allPassed = false;
    }
  }

  return allPassed;
}

function showHeader() {
  // Clear console only if not in CI/automated environment
  if (!process.env.CI && process.stdout.isTTY) {
    try {
      console.clear();
    } catch (e) {
      // Ignore clear errors in some environments
    }
  }
  console.log(colors.bright + colors.cyan);
  console.log('╔═══════════════════════════════════════════════════════════════╗');
  console.log('║                                                               ║');
  console.log('║                    🚀 AceServe Startup                        ║');
  console.log('║              AI-Powered Field Service Platform                ║');
  console.log('║                                                               ║');
  console.log('╚═══════════════════════════════════════════════════════════════╝');
  console.log(colors.reset);
}

function showHelp() {
  console.log(`
${colors.bright}Usage:${colors.reset} node start.js [options]

${colors.bright}Basic Options:${colors.reset}
  --skip-docker    Skip starting Docker services (use existing)
  --skip-migrate   Skip database migrations
  --skip-seed      Skip database seeding
  --no-browser     Don't open browser automatically
  --help           Show this help message

${colors.bright}Advanced Options:${colors.reset}
  --skip-tests     Skip comprehensive testing after startup
  --quick-start    Skip health checks and testing (faster startup)
  --retry-failed   Retry failed services automatically

${colors.bright}Services Started:${colors.reset}
  • PostgreSQL     (port 5432)
  • Redis          (port 6379)
  • Elasticsearch  (port 9200)
  • Backend API    (port ${config.services.backend.port})
  • Frontend       (port ${config.services.frontend.port})
  • Scraper        (port ${config.services.scraper.port})
  • Adminer        (port 8080)
  • Redis Commander(port 8081)

${colors.bright}Access URLs:${colors.reset}
  • Frontend:      ${config.urls.frontend}
  • Backend API:   ${config.urls.backend}
  • API Docs:      ${config.urls.api}
  • Scraper:       ${config.urls.scraper}
  • Database UI:   ${config.urls.adminer}
  • Redis UI:      ${config.urls.redisCommander}

${colors.bright}Default Credentials:${colors.reset}
  Email: <EMAIL>
  Password: DemoAdmin123!

${colors.bright}Progress Tracking:${colors.reset}
  Startup progress is saved to: progress.md
  `);
}

// Check if a command exists
function commandExists(command) {
  return new Promise((resolve) => {
    const checkCommand = os.platform() === 'win32' ? 'where' : 'which';
    exec(`${checkCommand} ${command}`, (error) => {
      resolve(!error);
    });
  });
}

// Check prerequisites
async function checkPrerequisites() {
  log('\n📋 Checking prerequisites...', colors.yellow);

  const checks = [
    { command: 'node', name: 'Node.js', required: true },
    { command: 'npm', name: 'npm', required: true },
    { command: 'docker', name: 'Docker', required: !options.skipDocker },
    { command: 'docker-compose', name: 'Docker Compose', required: !options.skipDocker },
    { command: 'python', name: 'Python', required: true }
  ];

  let allGood = true;
  let details = 'Prerequisites Check:\n';

  for (const check of checks) {
    if (!check.required) {
      details += `- ${check.name}: Skipped\n`;
      continue;
    }

    const exists = await commandExists(check.command);
    if (exists) {
      logSuccess(`${check.name} found`);
      details += `- ${check.name}: ✓ Found\n`;
    } else {
      logError(`${check.name} not found - please install ${check.name}`);
      details += `- ${check.name}: ✗ Missing\n`;
      allGood = false;
    }
  }

  // Check Node version
  if (allGood) {
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.split('.')[0].substring(1));
    if (majorVersion < 18) {
      logError(`Node.js version ${nodeVersion} is too old. Please upgrade to Node.js 18 or higher.`);
      details += `- Node.js Version: ✗ ${nodeVersion} (requires 18+)\n`;
      allGood = false;
    } else {
      logSuccess(`Node.js version ${nodeVersion} is compatible`);
      details += `- Node.js Version: ✓ ${nodeVersion}\n`;
    }
  }

  // Update progress
  startupProgress.prerequisites = allGood;
  saveProgress(details);

  return allGood;
}

// Create .env file if it doesn't exist
async function ensureEnvFile() {
  const envPath = path.join(__dirname, '.env');
  let details = 'Environment Setup:\n';

  if (!fs.existsSync(envPath)) {
    log('\n📝 Creating .env file with default values...', colors.yellow);

    const defaultEnv = `# AceServe Environment Configuration
# Generated by start.js

# Service Ports
BACKEND_PORT=3000
FRONTEND_PORT=3001
SCRAPER_PORT=8001

# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/aceserve

# Redis
REDIS_URL=redis://localhost:6379

# JWT Secrets (CHANGE THESE IN PRODUCTION!)
JWT_SECRET=your-super-secret-jwt-key-change-this
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this

# Session Secret
SESSION_SECRET=your-session-secret-change-this

# Frontend URL
FRONTEND_URL=http://localhost:3001

# API Configuration
ALLOWED_ORIGINS=http://localhost:3001,http://localhost:3000

# Gemini AI (Add your API key here)
GEMINI_API_KEY=your-gemini-api-key-here

# Scraper Service
SCRAPER_SERVICE_URL=http://localhost:8001

# Optional Services (uncomment and configure as needed)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# S3_BUCKET_NAME=aceserve-files
# SENDGRID_API_KEY=your-sendgrid-key
# TWILIO_ACCOUNT_SID=your-twilio-sid
# TWILIO_AUTH_TOKEN=your-twilio-token
# STRIPE_SECRET_KEY=your-stripe-key
`;

    fs.writeFileSync(envPath, defaultEnv);
    logSuccess('.env file created with default values');
    logWarning('Please update the JWT secrets and add your Gemini API key!');
    details += '- .env file: ✓ Created with defaults\n';
    details += '- Action required: Update JWT secrets and API keys\n';
  } else {
    logSuccess('.env file exists');
    details += '- .env file: ✓ Found\n';
  }

  // Update progress
  startupProgress.environment = true;
  saveProgress(details);
}

// Execute a command and return a promise
function execCommand(command, cwd = __dirname) {
  return new Promise((resolve, reject) => {
    exec(command, { cwd }, (error, stdout, stderr) => {
      if (error) {
        reject({ error, stderr });
      } else {
        resolve({ stdout, stderr });
      }
    });
  });
}

// Wait for a service to be ready
async function waitForService(name, checkCommand, maxRetries = 30, retryDelay = 2000) {
  logService('docker', `Waiting for ${name} to be ready...`);
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      await execCommand(checkCommand);
      logSuccess(`${name} is ready`);
      return true;
    } catch (error) {
      if (i < maxRetries - 1) {
        process.stdout.write('.');
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }
  
  logError(`${name} failed to start after ${maxRetries} attempts`);
  return false;
}

// Start Docker services
async function startDockerServices() {
  if (options.skipDocker) {
    log('\n⏭️  Skipping Docker services (--skip-docker flag)', colors.yellow);
    startupProgress.docker = true;
    saveProgress('Docker services: Skipped');
    return true;
  }

  logService('docker', 'Starting Docker services...');
  let details = 'Docker Services:\n';

  try {
    // Check if Docker daemon is running
    await execCommand('docker info');
    details += '- Docker daemon: ✓ Running\n';

    // Clean up any problematic containers first
    try {
      await execCommand('docker-compose down --remove-orphans', __dirname);
      details += '- Cleanup: ✓ Completed\n';
    } catch (e) {
      details += '- Cleanup: ⚠ Skipped (no existing containers)\n';
    }

    // For Windows, use docker-compose without building
    const isWindows = os.platform() === 'win32';
    const upCommand = isWindows
      ? 'docker-compose up -d --no-build postgres redis elasticsearch adminer redis-commander'
      : 'docker-compose up -d';

    // Start services
    await execCommand(upCommand, __dirname);
    logSuccess('Docker services started');
    details += '- Services started: ✓ PostgreSQL, Redis, Elasticsearch, Adminer, Redis Commander\n';

    // Wait for services to be ready
    const checks = [
      { name: 'PostgreSQL', command: 'docker exec aceserve-postgres pg_isready -U postgres' },
      { name: 'Redis', command: 'docker exec aceserve-redis redis-cli ping' }
    ];

    for (const check of checks) {
      const ready = await waitForService(check.name, check.command);
      if (ready) {
        details += `- ${check.name}: ✓ Ready\n`;
      } else {
        details += `- ${check.name}: ✗ Failed to start\n`;
        startupProgress.docker = false;
        saveProgress(details);
        return false;
      }
    }

    startupProgress.docker = true;
    saveProgress(details);
    return true;
  } catch (error) {
    logError('Failed to start Docker services');
    logError(error.stderr || error.message);
    details += `- Error: ${error.message}\n`;

    // If it's a build error, suggest running without build
    if (error.stderr && error.stderr.includes('failed to solve')) {
      log('\n💡 Try running with infrastructure services only:', colors.yellow);
      log('   docker-compose up -d postgres redis elasticsearch adminer redis-commander', colors.yellow);
      log('   Then run: node start.js --skip-docker', colors.yellow);
      details += '- Suggestion: Try infrastructure-only startup\n';
    }

    startupProgress.docker = false;
    saveProgress(details);
    return false;
  }
}

// Run database migrations
async function runMigrations() {
  let details = 'Database Setup:\n';

  if (options.skipMigrate) {
    log('\n⏭️  Skipping database migrations (--skip-migrate flag)', colors.yellow);
    details += '- Migrations: Skipped\n';
    startupProgress.database = true;
    saveProgress(details);
    return true;
  }

  logService('backend', 'Running database migrations...');

  try {
    await execCommand('npm run generate', config.paths.backend);
    details += '- Prisma generate: ✓ Completed\n';

    await execCommand('npm run migrate', config.paths.backend);
    logSuccess('Database migrations completed');
    details += '- Migrations: ✓ Completed\n';

    if (!options.skipSeed) {
      logService('backend', 'Seeding database...');
      await execCommand('npm run db:seed', config.paths.backend);
      logSuccess('Database seeded with demo data');
      details += '- Seeding: ✓ Demo data loaded\n';
    } else {
      details += '- Seeding: Skipped\n';
    }

    startupProgress.database = true;
    saveProgress(details);
    return true;
  } catch (error) {
    logError('Failed to run migrations');
    logError(error.stderr || error.message);
    details += `- Error: ${error.message}\n`;
    startupProgress.database = false;
    saveProgress(details);
    return false;
  }
}

// Install dependencies for a service
async function installDependencies(service, servicePath) {
  logService(service, 'Checking dependencies...');

  const packageJsonPath = path.join(servicePath, 'package.json');
  const nodeModulesPath = path.join(servicePath, 'node_modules');

  // Check if dependencies need to be installed or updated
  if (fs.existsSync(packageJsonPath)) {
    if (!fs.existsSync(nodeModulesPath)) {
      logService(service, 'Installing dependencies...');
      try {
        const npmCommand = os.platform() === 'win32' ? 'npm.cmd' : 'npm';
        await execCommand(`${npmCommand} install`, servicePath);
        logSuccess(`Dependencies installed for ${service}`);
      } catch (error) {
        logError(`Failed to install dependencies for ${service}`);
        throw error;
      }
    } else {
      // Check if next binary exists for frontend
      if (service === 'frontend') {
        const nextBinPath = path.join(servicePath, 'node_modules', '.bin', os.platform() === 'win32' ? 'next.cmd' : 'next');
        if (!fs.existsSync(nextBinPath)) {
          logService(service, 'Next.js binary missing, reinstalling dependencies...');
          try {
            const npmCommand = os.platform() === 'win32' ? 'npm.cmd' : 'npm';
            await execCommand(`${npmCommand} install`, servicePath);
            logSuccess(`Dependencies reinstalled for ${service}`);
          } catch (error) {
            logError(`Failed to reinstall dependencies for ${service}`);
            throw error;
          }
        }
      }
    }
  }
}

// Start a Node.js service with enhanced monitoring
function startNodeService(name, command, cwd) {
  return new Promise(async (resolve, reject) => {
    logService(name, `Starting ${config.services[name].name}...`);

    // On Windows, we need to ensure npm can find the local binaries
    const isWindows = os.platform() === 'win32';
    const npmCommand = isWindows ? 'npm.cmd' : 'npm';

    const proc = spawn(npmCommand, ['run', command], {
      cwd,
      shell: true,
      env: {
        ...process.env,
        FORCE_COLOR: '1',
        // Add node_modules/.bin to PATH for Windows
        PATH: isWindows
          ? `${path.join(cwd, 'node_modules', '.bin')};${process.env.PATH}`
          : process.env.PATH
      }
    });

    processes.set(name, proc);
    let serviceReady = false;

    proc.stdout.on('data', (data) => {
      const lines = data.toString().split('\n').filter(line => line.trim());
      lines.forEach(line => {
        logService(name, line);
      });

      // Check if service is ready
      if (data.toString().includes('Server listening') ||
          data.toString().includes('Ready on') ||
          data.toString().includes('compiled successfully')) {
        logSuccess(`${config.services[name].name} is ready on port ${config.services[name].port}`);
        serviceReady = true;

        // Perform health check if not in quick start mode
        if (!options.quickStart) {
          setTimeout(async () => {
            const healthOk = await validateServiceHealth(name);
            if (healthOk) {
              startupProgress[name] = true;
              saveProgress(`${config.services[name].name}: ✓ Started and healthy`);
            }
            resolve();
          }, 2000); // Wait 2 seconds for service to fully initialize
        } else {
          startupProgress[name] = true;
          saveProgress(`${config.services[name].name}: ✓ Started (health check skipped)`);
          resolve();
        }
      }
    });

    proc.stderr.on('data', (data) => {
      const lines = data.toString().split('\n').filter(line => line.trim());
      lines.forEach(line => {
        if (!line.includes('DeprecationWarning')) {
          logService(name, `${colors.red}ERROR: ${line}${colors.reset}`);
        }
      });
    });

    proc.on('error', (error) => {
      logError(`Failed to start ${config.services[name].name}: ${error.message}`);
      startupProgress[name] = false;
      saveProgress(`${config.services[name].name}: ✗ Failed to start - ${error.message}`);
      reject(error);
    });

    proc.on('exit', (code) => {
      if (!isShuttingDown) {
        logError(`${config.services[name].name} exited with code ${code}`);
        startupProgress[name] = false;
        saveProgress(`${config.services[name].name}: ✗ Exited with code ${code}`);
      }
      processes.delete(name);
    });

    // Set a timeout for service startup
    setTimeout(() => {
      if (processes.has(name) && !serviceReady) {
        logWarning(`${config.services[name].name} startup timeout, assuming ready`);
        startupProgress[name] = true;
        saveProgress(`${config.services[name].name}: ⚠ Started (timeout, assumed ready)`);
        resolve();
      }
    }, config.timeouts.serviceStart);
  });
}

// Start Python scraper service
function startPythonService() {
  return new Promise((resolve, reject) => {
    logService('scraper', 'Starting Scraper Service...');

    const venvPath = path.join(config.paths.scraper, 'venv');
    const pythonCmd = os.platform() === 'win32'
      ? path.join(venvPath, 'Scripts', 'python.exe')
      : path.join(venvPath, 'bin', 'python');

    // Check if venv exists, if not create it
    if (!fs.existsSync(venvPath)) {
      logService('scraper', 'Creating Python virtual environment...');
      exec('python -m venv venv', { cwd: config.paths.scraper }, (error) => {
        if (error) {
          logError('Failed to create virtual environment');
          startupProgress.scraper = false;
          saveProgress('Scraper Service: ✗ Failed to create virtual environment');
          reject(error);
          return;
        }

        // Install requirements
        logService('scraper', 'Installing Python dependencies...');
        const pipCmd = os.platform() === 'win32'
          ? path.join(venvPath, 'Scripts', 'pip.exe')
          : path.join(venvPath, 'bin', 'pip');

        exec(`${pipCmd} install -r requirements.txt`, { cwd: config.paths.scraper }, (error) => {
          if (error) {
            logError('Failed to install Python dependencies');
            startupProgress.scraper = false;
            saveProgress('Scraper Service: ✗ Failed to install dependencies');
            reject(error);
            return;
          }

          startScraperProcess();
        });
      });
    } else {
      startScraperProcess();
    }

    function startScraperProcess() {
      const proc = spawn(pythonCmd, ['main.py'], {
        cwd: config.paths.scraper,
        env: { ...process.env }
      });

      processes.set('scraper', proc);
      let serviceReady = false;

      proc.stdout.on('data', (data) => {
        const lines = data.toString().split('\n').filter(line => line.trim());
        lines.forEach(line => {
          logService('scraper', line);
        });

        if (data.toString().includes('Uvicorn running on')) {
          const port = config.services.scraper.port;
          logSuccess(`Scraper Service is ready on port ${port}`);
          serviceReady = true;

          // Perform health check if not in quick start mode
          if (!options.quickStart) {
            setTimeout(async () => {
              const healthOk = await validateServiceHealth('scraper');
              if (healthOk) {
                startupProgress.scraper = true;
                saveProgress(`Scraper Service: ✓ Started and healthy on port ${port}`);
              }
              resolve();
            }, 2000);
          } else {
            startupProgress.scraper = true;
            saveProgress(`Scraper Service: ✓ Started on port ${port} (health check skipped)`);
            resolve();
          }
        }
      });

      proc.stderr.on('data', (data) => {
        const lines = data.toString().split('\n').filter(line => line.trim());
        lines.forEach(line => {
          logService('scraper', `${colors.red}ERROR: ${line}${colors.reset}`);
        });
      });

      proc.on('error', (error) => {
        logError(`Failed to start Scraper Service: ${error.message}`);
        startupProgress.scraper = false;
        saveProgress(`Scraper Service: ✗ Failed to start - ${error.message}`);
        reject(error);
      });

      proc.on('exit', (code) => {
        if (!isShuttingDown) {
          logError(`Scraper Service exited with code ${code}`);
          startupProgress.scraper = false;
          saveProgress(`Scraper Service: ✗ Exited with code ${code}`);
        }
        processes.delete('scraper');
      });

      // Set a timeout for service startup
      setTimeout(() => {
        if (processes.has('scraper') && !serviceReady) {
          logWarning('Scraper Service startup timeout, assuming ready');
          startupProgress.scraper = true;
          saveProgress('Scraper Service: ⚠ Started (timeout, assumed ready)');
          resolve();
        }
      }, config.timeouts.serviceStart);
    }
  });
}

// Comprehensive application testing
async function runComprehensiveTests() {
  if (options.skipTests) {
    log('\n⏭️  Skipping comprehensive tests (--skip-tests flag)', colors.yellow);
    startupProgress.testing = true;
    saveProgress('Testing: Skipped');
    return true;
  }

  log('\n🧪 Running comprehensive application tests...', colors.yellow);
  let details = 'Comprehensive Testing:\n';
  let allTestsPassed = true;

  try {
    // Test 1: Health check all services
    log('Testing service health endpoints...', colors.cyan);
    const healthChecks = await Promise.all([
      validateServiceHealth('backend'),
      validateServiceHealth('frontend'),
      validateServiceHealth('scraper')
    ]);

    if (healthChecks.every(check => check)) {
      logSuccess('All service health checks passed');
      details += '- Service health checks: ✓ All passed\n';
    } else {
      logError('Some service health checks failed');
      details += '- Service health checks: ✗ Some failed\n';
      allTestsPassed = false;
    }

    // Test 2: Service dependencies
    log('Testing service dependencies...', colors.cyan);
    const dependenciesOk = await validateServiceDependencies();
    if (dependenciesOk) {
      logSuccess('Service dependencies validation passed');
      details += '- Service dependencies: ✓ All connected\n';
    } else {
      logError('Service dependencies validation failed');
      details += '- Service dependencies: ✗ Some failed\n';
      allTestsPassed = false;
    }

    // Test 3: Basic API endpoints
    log('Testing basic API endpoints...', colors.cyan);
    const apiTests = [
      { name: 'Backend Health', url: `${config.urls.backend}/health` },
      { name: 'API Documentation', url: `${config.urls.api}` },
      { name: 'Scraper Health', url: `${config.urls.scraper}/health` }
    ];

    let apiTestsPassed = 0;
    for (const test of apiTests) {
      const result = await httpHealthCheck(test.url);
      if (result.success) {
        logSuccess(`${test.name} endpoint accessible`);
        apiTestsPassed++;
      } else {
        logError(`${test.name} endpoint failed: ${result.error || `HTTP ${result.statusCode}`}`);
      }
    }

    if (apiTestsPassed === apiTests.length) {
      details += '- API endpoints: ✓ All accessible\n';
    } else {
      details += `- API endpoints: ⚠ ${apiTestsPassed}/${apiTests.length} accessible\n`;
      if (apiTestsPassed === 0) allTestsPassed = false;
    }

    // Test 4: Run existing test suite if available
    if (fs.existsSync(path.join(config.paths.backend, 'test', 'e2e', 'quickTest.js'))) {
      log('Running existing quick test suite...', colors.cyan);
      try {
        await execCommand('npm run test:e2e:quick', config.paths.backend);
        logSuccess('Quick test suite passed');
        details += '- Quick test suite: ✓ Passed\n';
      } catch (error) {
        logWarning('Quick test suite had issues (this may be expected in development)');
        details += '- Quick test suite: ⚠ Had issues\n';
      }
    }

    startupProgress.testing = allTestsPassed;
    saveProgress(details);

    if (allTestsPassed) {
      logSuccess('Comprehensive testing completed successfully');
    } else {
      logWarning('Comprehensive testing completed with some issues');
    }

    return allTestsPassed;
  } catch (error) {
    logError(`Testing failed: ${error.message}`);
    details += `- Error: ${error.message}\n`;
    startupProgress.testing = false;
    saveProgress(details);
    return false;
  }
}

// Open browser
function openBrowser(url) {
  const platform = os.platform();
  let command;

  switch (platform) {
    case 'darwin':
      command = `open ${url}`;
      break;
    case 'win32':
      command = `start ${url}`;
      break;
    default:
      command = `xdg-open ${url}`;
  }

  exec(command, (error) => {
    if (error) {
      log(`\n⚠️  Could not open browser automatically. Please navigate to: ${url}`, colors.yellow);
    }
  });
}

// Graceful shutdown
function shutdown() {
  if (isShuttingDown) return;
  isShuttingDown = true;
  
  log('\n\n🛑 Shutting down services...', colors.yellow);
  
  // Kill all processes
  processes.forEach((proc, name) => {
    logService(name, 'Stopping...');
    if (os.platform() === 'win32') {
      spawn('taskkill', ['/pid', proc.pid, '/f', '/t']);
    } else {
      proc.kill('SIGTERM');
    }
  });
  
  // Wait a bit for processes to stop
  setTimeout(() => {
    log('\n👋 Goodbye!', colors.cyan);
    process.exit(0);
  }, 2000);
}

// Main startup sequence
async function main() {
  showHeader();

  if (options.help) {
    showHelp();
    process.exit(0);
  }

  // Set up shutdown handlers
  process.on('SIGINT', shutdown);
  process.on('SIGTERM', shutdown);
  process.on('exit', shutdown);

  // Initialize progress tracking
  saveProgress('Startup initiated');

  try {
    // Check prerequisites
    const prerequisitesOk = await checkPrerequisites();
    if (!prerequisitesOk) {
      logError('\nPrerequisites check failed. Please install missing dependencies.');
      process.exit(1);
    }

    // Ensure .env file exists
    await ensureEnvFile();

    // Start Docker services
    log('\n🐳 Starting Docker services...', colors.blue);
    const dockerOk = await startDockerServices();
    if (!dockerOk) {
      logError('\nFailed to start Docker services');
      process.exit(1);
    }

    // Install dependencies
    log('\n📦 Installing dependencies...', colors.yellow);
    let dependenciesDetails = 'Dependencies Installation:\n';
    try {
      await installDependencies('backend', config.paths.backend);
      dependenciesDetails += '- Backend: ✓ Installed\n';
      await installDependencies('frontend', config.paths.frontend);
      dependenciesDetails += '- Frontend: ✓ Installed\n';
      startupProgress.dependencies = true;
      saveProgress(dependenciesDetails);
    } catch (error) {
      dependenciesDetails += `- Error: ${error.message}\n`;
      startupProgress.dependencies = false;
      saveProgress(dependenciesDetails);
      throw error;
    }

    // Run migrations
    log('\n🗄️  Setting up database...', colors.yellow);
    const migrationsOk = await runMigrations();
    if (!migrationsOk) {
      logError('\nFailed to set up database');
      process.exit(1);
    }

    // Start services
    log('\n🚀 Starting application services...', colors.green);

    // Start backend first (other services depend on it)
    await startNodeService('backend', 'dev', config.paths.backend);

    // Wait for backend to be fully ready before starting dependent services
    if (!options.quickStart) {
      log('Waiting for backend to be fully ready...', colors.yellow);
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    // Start frontend and scraper in parallel
    await Promise.all([
      startNodeService('frontend', 'dev', config.paths.frontend),
      startPythonService()
    ]);

    // Run comprehensive tests
    log('\n🧪 Running comprehensive validation...', colors.yellow);
    const testsOk = await runComprehensiveTests();

    // All services started successfully
    log('\n' + colors.bright + colors.green);
    log('╔═══════════════════════════════════════════════════════════════╗');
    log('║                                                               ║');
    log('║              ✅ AceServe is ready!                            ║');
    log('║                                                               ║');
    log('╚═══════════════════════════════════════════════════════════════╝');
    log(colors.reset);

    log('\n📍 Access Points:', colors.cyan);
    log(`  • Frontend:        ${colors.bright}${config.urls.frontend}${colors.reset}`);
    log(`  • Backend API:     ${colors.bright}${config.urls.backend}${colors.reset}`);
    log(`  • API Docs:        ${colors.bright}${config.urls.api}${colors.reset}`);
    log(`  • Scraper Service: ${colors.bright}${config.urls.scraper}${colors.reset}`);
    log(`  • Database UI:     ${colors.bright}${config.urls.adminer}${colors.reset}`);
    log(`  • Redis UI:        ${colors.bright}${config.urls.redisCommander}${colors.reset}`);

    log('\n🔑 Default Login:', colors.cyan);
    log(`  • Email:    ${colors.bright}<EMAIL>${colors.reset}`);
    log(`  • Password: ${colors.bright}DemoAdmin123!${colors.reset}`);

    log('\n📊 Startup Summary:', colors.cyan);
    const completedSteps = Object.values(startupProgress).filter(Boolean).length;
    const totalSteps = Object.keys(startupProgress).length;
    log(`  • Steps completed: ${colors.bright}${completedSteps}/${totalSteps}${colors.reset}`);
    log(`  • Progress file: ${colors.bright}progress.md${colors.reset}`);
    if (!testsOk) {
      log(`  • Status: ${colors.yellow}Ready with warnings${colors.reset}`);
    } else {
      log(`  • Status: ${colors.green}Fully operational${colors.reset}`);
    }

    log('\n💡 Tips:', colors.yellow);
    log('  • Press Ctrl+C to stop all services');
    log('  • Logs are displayed in real-time with timestamps');
    log('  • Check progress.md for detailed startup status');
    log('  • Use --help to see all available options');

    // Open browser
    if (!options.noBrowser) {
      setTimeout(() => {
        log('\n🌐 Opening browser...', colors.cyan);
        openBrowser(config.urls.frontend);
      }, 3000);
    }

    // Final progress update
    saveProgress('Startup completed successfully');

  } catch (error) {
    logError(`\nStartup failed: ${error.message}`);
    console.error(error);
    saveProgress(`Startup failed: ${error.message}`);
    shutdown();
  }
}

// Start the application
main().catch(error => {
  console.error('Fatal error during startup:', error);
  process.exit(1);
});